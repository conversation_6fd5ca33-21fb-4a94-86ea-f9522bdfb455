import { useState, useEffect } from 'react';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
  MDBBtn
} from 'mdb-react-ui-kit';

const ClientPXEReinstallModal = ({ 
  show, 
  onHide, 
  server, 
  serverType = 'dedicated',
  onReinstallComplete 
}) => {
  const [operatingSystems, setOperatingSystems] = useState([]);
  const [selectedOS, setSelectedOS] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isInstalling, setIsInstalling] = useState(false);
  const [installProgress, setInstallProgress] = useState('');
  const [customRootPassword, setCustomRootPassword] = useState('');
  const [useCustomPassword, setUseCustomPassword] = useState(false);
  const [installationStatus, setInstallationStatus] = useState(null);
  const [checkingStatus, setCheckingStatus] = useState(false);

  // Fetch available operating systems
  const fetchOperatingSystems = async () => {
    try {
      setLoading(true);
      setError('');

      const token = sessionStorage.getItem('token');
      console.log('Token for OS fetch:', token ? `${token.substring(0, 10)}...` : 'null');
      const response = await fetch('/api.php?f=client_reinstallable_os', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        console.error('API Error:', data);
        throw new Error(data.message || data.error || 'Unknown error');
      }
      
      if (Array.isArray(data)) {
        setOperatingSystems(data);
        if (data.length > 0) {
          setSelectedOS(data[0].id.toString());
        }
      } else {
        throw new Error('Invalid response format');
      }

    } catch (err) {
      console.error('Error fetching operating systems:', err);
      setError(`Failed to load operating systems: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Check installation status
  const checkInstallationStatus = async () => {
    if (!server?.id) return;

    try {
      setCheckingStatus(true);
      const token = sessionStorage.getItem('token');
      
      const response = await fetch('/api.php?f=client_pxe_status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: server.id,
          server_type: serverType
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        console.error('Status check error:', data);
        console.error('Status check error message:', data.message || data.error);
        return;
      }

      setInstallationStatus(data);
      
      if (data.has_installation && data.is_active) {
        setIsInstalling(true);
        setInstallProgress(`Installation in progress (${data.status})`);
      }

    } catch (err) {
      console.error('Error checking installation status:', err);
    } finally {
      setCheckingStatus(false);
    }
  };

  // Start PXE reinstall
  const startPXEReinstall = async () => {
    if (!selectedOS) {
      setError('Please select an operating system');
      return;
    }

    if (!server?.id) {
      setError('Server information is not available');
      return;
    }

    try {
      setLoading(true);
      setIsInstalling(true);
      setError('');
      setInstallProgress('Initializing PXE reinstall...');

      const token = sessionStorage.getItem('token');
      
      const requestData = {
        token,
        server_id: server.id,
        server_type: serverType,
        os_id: selectedOS,
        custom_root_password: useCustomPassword ? customRootPassword : null
      };

      console.log('Starting PXE reinstall with configuration:', requestData);

      const response = await fetch('/api.php?f=client_pxe_reinstall', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('PXE reinstall failed with HTTP error:', response.status, errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('PXE reinstall response:', result);

      if (!result.success) {
        throw new Error(result.error || 'Unknown error occurred');
      }

      setInstallProgress('PXE reinstall initiated successfully! Server will reboot and begin installation.');
      
      // Notify parent component
      if (onReinstallComplete) {
        onReinstallComplete(result);
      }

      // Auto-close modal after success
      setTimeout(() => {
        handleClose();
      }, 3000);

    } catch (err) {
      console.error('Error starting PXE reinstall:', err);
      setError(`Failed to start reinstall: ${err.message}`);
      setIsInstalling(false);
      
      // Check status after error to update UI
      setTimeout(() => {
        checkInstallationStatus();
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedOS('');
    setError('');
    setInstallProgress('');
    setCustomRootPassword('');
    setUseCustomPassword(false);
    setIsInstalling(false);
    setInstallationStatus(null);
    setCheckingStatus(false);
    onHide();
  };

  // Load data when modal opens
  useEffect(() => {
    if (show) {
      fetchOperatingSystems();
      checkInstallationStatus();
    }
  }, [show, server?.id]);

  return (
    <MDBModal show={show} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>Reinstall Operating System</MDBModalTitle>
            <button type="button" className="close" onClick={handleClose}>
              <span aria-hidden="true">&times;</span>
            </button>
          </MDBModalHeader>

          <MDBModalBody>


            {/* Installation Status Check */}
            {checkingStatus && (
              <div className="text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status"></div>
                <p>Checking installation status...</p>
              </div>
            )}

            {/* Active Installation Warning */}
            {installationStatus?.has_installation && installationStatus?.is_active && (
              <div className="alert alert-warning mb-3">
                <div className="d-flex">
                  <div className="flex-shrink-0">
                    <i className="fas fa-exclamation-triangle text-warning"></i>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    <strong>Installation in Progress</strong>
                    <br />
                    Status: {installationStatus.status}
                    <br />
                    Started: {new Date(installationStatus.initiated_at).toLocaleString()}
                    <br />
                    OS: {installationStatus.os_template}
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="alert alert-danger mb-3">
                <div className="d-flex">
                  <div className="flex-shrink-0">
                    <i className="fas fa-exclamation-circle text-danger"></i>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    {error}
                  </div>
                </div>
              </div>
            )}

            {/* Installation Progress */}
            {isInstalling && installProgress && (
              <div className="alert alert-info mb-3">
                <div className="d-flex align-items-center">
                  <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                  <div className="flex-grow-1">
                    {installProgress}
                  </div>
                </div>
              </div>
            )}

        {/* OS Selection Form */}
        {!isInstalling && (
          <>
            <div className="mb-4">
              <label className="form-label">
                Select Operating System <span className="text-danger">*</span>
              </label>
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary mb-3" role="status"></div>
                  <p>Loading operating systems...</p>
                </div>
              ) : (
                <select
                  className="form-control form-select"
                  value={selectedOS}
                  onChange={(e) => setSelectedOS(e.target.value)}
                  disabled={loading}
                >
                  <option value="">Select an operating system</option>
                  {operatingSystems.map(os => (
                    <option key={os.id} value={os.id.toString()}>
                      {os.name}
                    </option>
                  ))}
                </select>
              )}
            </div>

            {/* Custom Root Password */}
            <div className="mb-3">
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="useCustomPassword"
                  checked={useCustomPassword}
                  onChange={(e) => setUseCustomPassword(e.target.checked)}
                                  style={{ 
        marginTop: '15px',
        marginLeft: '2px'
      }}
                />
                <label className="form-check-label" htmlFor="useCustomPassword"s>
                  Set custom root password
                </label>
              </div>
            </div>

            {useCustomPassword && (
              <div className="mb-4">
                <label className="form-label">Custom Root Password</label>
                <input
                  type="password"
                  className="form-control"
                  value={customRootPassword}
                  onChange={(e) => setCustomRootPassword(e.target.value)}
                  placeholder="Enter custom root password"
                />
                <div className="form-text text-muted">
                  Leave empty to use system-generated password
                </div>
              </div>
            )}

            {/* Warning */}
            <div className="alert alert-warning">
              <strong>Warning:</strong> This will completely wipe your server and install a fresh operating system.
              All data will be permanently lost. Make sure you have backups of any important data.
            </div>
          </>
            )}
          </MDBModalBody>

          <MDBModalFooter>
            <MDBBtn color='secondary' onClick={handleClose} disabled={loading && isInstalling}>
              {isInstalling ? 'Close' : 'Cancel'}
            </MDBBtn>

            {!isInstalling && (
              <MDBBtn
                color='danger'
                onClick={startPXEReinstall}
                disabled={loading || !selectedOS || (installationStatus?.has_installation && installationStatus?.is_active)}
              >
                {loading ? (
                  <>
                    <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                    Starting...
                  </>
                ) : (
                  'Reinstall Server'
                )}
              </MDBBtn>
            )}
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default ClientPXEReinstallModal;
